@startuml 敬老院管理系统架构图
!theme plain
skinparam backgroundColor #FFFFFF
skinparam componentStyle rectangle
skinparam linetype ortho

title 敬老院管理系统架构图

' 定义颜色
skinparam component {
    BackgroundColor<<frontend>> #E3F2FD
    BackgroundColor<<backend>> #E8F5E8
    BackgroundColor<<database>> #FFF3E0
    BackgroundColor<<cache>> #FCE4EC
    BackgroundColor<<security>> #F3E5F5
    BackgroundColor<<middleware>> #E0F2F1
}

' 用户层
actor "系统用户" as user

' 前端层
package "前端层 (端口: 8080)" as frontend_layer {
    component "Vue 3 应用" as vue <<frontend>> {
        component "Vue Router 4" as router
        component "Vuex 4" as vuex
        component "Element Plus" as element
        component "Axios" as axios
        component "TypeScript" as ts
    }
    
    component "Nginx 服务器" as nginx <<middleware>> {
        note right: 静态资源服务\n反向代理
    }
}

' 后端层
package "后端层 (端口: 9001)" as backend_layer {
    component "Spring Boot 2.6.1" as springboot <<backend>> {
        component "Controller 层" as controller
        component "Service 层" as service
        component "MyBatis Plus *******" as mybatis
    }
    
    component "Apache Shiro 1.9.1" as shiro <<security>> {
        component "JWT 认证" as jwt
        component "权限控制" as auth
        component "AES 加密" as aes
    }
    
    component "Druid 连接池" as druid <<middleware>> {
        note right: 数据库连接管理\n性能监控
    }
}

' 数据层
package "数据层" as data_layer {
    database "MySQL 5.7\n(端口: 3306)" as mysql <<database>> {
        component "db_gerocomium" as db
        note right: 用户信息\n老人信息\n床位管理\n咨询记录\n入住签约\n服务预定\n退住申请
    }
    
    database "Redis 6.2.13\n(端口: 6379)" as redis <<cache>> {
        component "用户会话" as session
        component "权限缓存" as permission_cache
        component "数据字典" as dict_cache
        component "JWT Token" as token_cache
        note right: 分布式会话管理\n权限数据缓存\n业务数据缓存
    }
}

' 部署层
package "容器化部署" as deploy_layer {
    component "Docker Compose" as compose <<middleware>> {
        component "Frontend Container" as frontend_container
        component "Backend Container" as backend_container
        component "MySQL Container" as mysql_container
        component "Redis Container" as redis_container
    }
    
    component "Docker Network" as network <<middleware>> {
        note right: app-network\n容器间通信
    }
}

' 连接关系
user --> nginx : HTTP/HTTPS 请求
nginx --> vue : 静态资源服务
nginx --> springboot : API 代理 (/api/*)

vue --> axios : HTTP 客户端
axios --> springboot : RESTful API

springboot --> shiro : 安全认证
shiro --> jwt : Token 验证
shiro --> redis : 会话管理

springboot --> druid : 连接池
druid --> mysql : 数据库连接

springboot --> redis : 缓存操作
mybatis --> mysql : ORM 映射

' 部署关系
frontend_container --> nginx
backend_container --> springboot
mysql_container --> mysql
redis_container --> redis

compose --> frontend_container
compose --> backend_container
compose --> mysql_container
compose --> redis_container

network --> frontend_container
network --> backend_container
network --> mysql_container
network --> redis_container

' 技术栈说明
note top of frontend_layer
<b>前端技术栈</b>
• Vue 3 + TypeScript
• Element Plus UI
• Vue Router 4 路由
• Vuex 4 状态管理
• Axios HTTP客户端
• SCSS + Tailwind CSS
• ECharts 图表
end note

note top of backend_layer
<b>后端技术栈</b>
• Spring Boot 2.6.1
• Apache Shiro 1.9.1
• MyBatis Plus *******
• JWT 无状态认证
• Druid 连接池
• Hutool 工具库
• Swagger API文档
end note

note top of data_layer
<b>数据存储</b>
• MySQL 5.7 关系型数据库
• Redis 6.2.13 内存缓存
• AES 密码加密
• 分布式会话管理
end note

note top of deploy_layer
<b>部署架构</b>
• Docker 容器化
• Docker Compose 编排
• Nginx 反向代理
• 阿里云镜像加速
• CentOS 7.9 服务器
end note

@enduml
