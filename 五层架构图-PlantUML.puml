@startuml 敬老院管理系统五层架构图
!theme plain
skinparam backgroundColor #FFFFFF
skinparam componentStyle rectangle
skinparam linetype ortho

title 敬老院管理系统五层架构图

' 定义颜色
skinparam component {
    BackgroundColor<<presentation>> #E3F2FD
    BackgroundColor<<security>> #F3E5F5
    BackgroundColor<<business>> #E8F5E8
    BackgroundColor<<data_access>> #FFF3E0
    BackgroundColor<<data_storage>> #FCE4EC
}

' 用户层
actor "系统用户" as user

' 第一层：表示层
package "表示层 (Presentation Layer)" as presentation_layer {
    ' 前端部分
    component "前端表示层" as frontend <<presentation>> {
        component "Vue 3 + TypeScript" as vue
        component "Vue Router 4" as router
        component "Vuex 4" as vuex
        component "Element Plus" as element
        component "Views组件" as views {
            component "home/index.vue" as home_view
            component "login/index.vue" as login_view
            component "Layout组件" as layout
        }
    }
    
    ' 后端Controller层
    component "后端Controller层" as backend_controller <<presentation>> {
        component "AccountController" as account_ctrl
        component "BuildController" as build_ctrl
        component "OrderController" as order_ctrl
        component "RESTful API" as rest_api
        component "Swagger文档" as swagger
    }
}

' 第二层：安全层
package "安全层 (Security Layer)" as security_layer {
    component "Apache Shiro 1.9.1" as shiro <<security>> {
        component "ShiroConfig" as shiro_config
        component "JwtRealm" as jwt_realm
        component "JwtFilter" as jwt_filter
        component "JwtToken" as jwt_token
        component "ShiroUtils" as shiro_utils
        component "PasswordEncoder" as password_encoder
        component "AuthorityAssert" as authority_assert
    }
}

' 第三层：业务逻辑层
package "业务逻辑层 (Business Logic Layer)" as business_layer {
    component "Service层" as service_layer <<business>> {
        component "Service接口" as service_interface {
            component "AccountService" as account_service
            component "BuildService" as build_service
            component "OrderService" as order_service
        }
        component "ServiceImpl实现类" as service_impl {
            component "AccountServiceImpl" as account_service_impl
            component "BuildServiceImpl" as build_service_impl
            component "OrderServiceImpl" as order_service_impl
        }
        component "Func功能类" as func_classes {
            component "BuildingFunc" as building_func
            component "ElderFunc" as elder_func
            component "BedFunc" as bed_func
        }
    }
}

' 第四层：数据访问层
package "数据访问层 (Data Access Layer)" as data_access_layer {
    component "MyBatis Plus 3.4.3.4" as mybatis <<data_access>> {
        component "Mapper接口" as mapper_interface {
            component "OrderMapper" as order_mapper
            component "ElderMapper" as elder_mapper
            component "BedMapper" as bed_mapper
        }
        component "实体类(PO)" as entity_po {
            component "Elder" as elder_po
            component "Bed" as bed_po
            component "Order" as order_po
        }
        component "查询对象(Query)" as query_objects {
            component "AddOrderQuery" as add_order_query
            component "PageOrderByKeyQuery" as page_order_query
        }
        component "响应对象(VO)" as vo_objects {
            component "LoginUserVo" as login_user_vo
            component "GetOrderByIdVo" as get_order_vo
        }
        component "XML映射文件" as xml_mapper
    }
}

' 第五层：数据存储层
package "数据存储层 (Data Storage Layer)" as data_storage_layer {
    database "MySQL 5.7" as mysql <<data_storage>> {
        component "db_gerocomium" as database {
            component "用户相关表" as user_tables {
                component "staff(员工表)" as staff_table
                component "auth(权限表)" as auth_table
                component "role(角色表)" as role_table
            }
            component "业务相关表" as business_tables {
                component "elder(老人表)" as elder_table
                component "bed(床位表)" as bed_table
                component "order(订餐表)" as order_table
            }
            component "管理相关表" as management_tables {
                component "building(楼栋表)" as building_table
                component "floor(楼层表)" as floor_table
                component "room(房间表)" as room_table
            }
        }
    }
    
    database "Redis 6.2.13" as redis <<data_storage>> {
        component "缓存数据" as cache_data {
            component "用户会话管理" as user_session
            component "权限数据缓存" as permission_cache
            component "JWT Token缓存" as jwt_cache
            component "业务数据缓存" as business_cache
        }
    }
    
    component "Druid连接池" as druid <<data_storage>> {
        note right: 数据库连接管理\n性能监控
    }
}

' 连接关系
user --> frontend : 用户交互
frontend --> backend_controller : HTTP请求

backend_controller --> shiro : 权限验证
shiro --> service_layer : 认证通过

service_layer --> mybatis : 数据操作

mybatis --> mysql : SQL查询
mybatis --> redis : 缓存操作
druid --> mysql : 连接管理

' 内部连接
vue --> router
vue --> vuex
vue --> element
views --> layout

account_ctrl --> rest_api
build_ctrl --> rest_api
order_ctrl --> rest_api
rest_api --> swagger

shiro_config --> jwt_realm
jwt_realm --> jwt_filter
jwt_filter --> jwt_token
shiro_utils --> password_encoder
shiro_utils --> authority_assert

service_interface --> service_impl
service_impl --> func_classes

mapper_interface --> entity_po
mapper_interface --> query_objects
mapper_interface --> vo_objects
mapper_interface --> xml_mapper

user_tables --> staff_table
user_tables --> auth_table
user_tables --> role_table

business_tables --> elder_table
business_tables --> bed_table
business_tables --> order_table

management_tables --> building_table
management_tables --> floor_table
management_tables --> room_table

cache_data --> user_session
cache_data --> permission_cache
cache_data --> jwt_cache
cache_data --> business_cache

' 层次说明
note top of presentation_layer
<b>表示层</b>
负责用户界面展示和用户交互
• 前端: Vue 3 + Element Plus
• 后端: Spring Boot Controller
• 功能: 页面渲染、用户输入处理、API接口
end note

note top of security_layer
<b>安全层</b>
负责系统安全认证和授权
• Apache Shiro 安全框架
• JWT 无状态认证
• AES 密码加密
• 权限控制和访问验证
end note

note top of business_layer
<b>业务逻辑层</b>
负责核心业务逻辑处理
• Service 业务服务
• 数据验证和业务规则
• 事务管理
• 业务流程控制
end note

note top of data_access_layer
<b>数据访问层</b>
负责数据持久化操作
• MyBatis Plus ORM框架
• Mapper 数据访问接口
• 实体类和数据传输对象
• SQL映射和数据转换
end note

note top of data_storage_layer
<b>数据存储层</b>
负责数据存储和管理
• MySQL 关系型数据库
• Redis 内存缓存
• Druid 数据库连接池
• 数据持久化和缓存策略
end note

@enduml
