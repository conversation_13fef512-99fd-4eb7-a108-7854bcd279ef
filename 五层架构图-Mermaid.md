# 敬老院管理系统五层架构图 (Mermaid)

```mermaid
graph TB
    %% 样式定义
    classDef presentationStyle fill:#E3F2FD,stroke:#1976D2,stroke-width:2px
    classDef securityStyle fill:#F3E5F5,stroke:#7B1FA2,stroke-width:2px
    classDef businessStyle fill:#E8F5E8,stroke:#388E3C,stroke-width:2px
    classDef dataAccessStyle fill:#FFF3E0,stroke:#F57C00,stroke-width:2px
    classDef dataStorageStyle fill:#FCE4EC,stroke:#C2185B,stroke-width:2px
    classDef userStyle fill:#FFEB3B,stroke:#F57F17,stroke-width:3px

    %% 用户层
    User[系统用户]:::userStyle

    %% 第一层：表示层
    subgraph PresentationLayer["表示层 (Presentation Layer)"]
        %% 前端部分
        subgraph Frontend["前端表示层"]
            Vue["Vue 3 + TypeScript"]
            Router["Vue Router 4"]
            Vuex["Vuex 4"]
            Element["Element Plus"]
            
            subgraph Views["Views组件"]
                HomeView["home/index.vue"]
                LoginView["login/index.vue"]
                Layout["Layout组件"]
            end
        end
        
        %% 后端Controller层
        subgraph BackendController["后端Controller层"]
            AccountCtrl["AccountController"]
            BuildCtrl["BuildController"]
            OrderCtrl["OrderController"]
            RestAPI["RESTful API"]
            Swagger["Swagger文档"]
        end
    end

    %% 第二层：安全层
    subgraph SecurityLayer["安全层 (Security Layer)"]
        subgraph Shiro["Apache Shiro 1.9.1"]
            ShiroConfig["ShiroConfig"]
            JwtRealm["JwtRealm"]
            JwtFilter["JwtFilter"]
            JwtToken["JwtToken"]
            ShiroUtils["ShiroUtils"]
            PasswordEncoder["PasswordEncoder"]
            AuthorityAssert["AuthorityAssert"]
        end
    end

    %% 第三层：业务逻辑层
    subgraph BusinessLayer["业务逻辑层 (Business Logic Layer)"]
        subgraph ServiceLayer["Service层"]
            subgraph ServiceInterface["Service接口"]
                AccountService["AccountService"]
                BuildService["BuildService"]
                OrderService["OrderService"]
            end
            
            subgraph ServiceImpl["ServiceImpl实现类"]
                AccountServiceImpl["AccountServiceImpl"]
                BuildServiceImpl["BuildServiceImpl"]
                OrderServiceImpl["OrderServiceImpl"]
            end
            
            subgraph FuncClasses["Func功能类"]
                BuildingFunc["BuildingFunc"]
                ElderFunc["ElderFunc"]
                BedFunc["BedFunc"]
            end
        end
    end

    %% 第四层：数据访问层
    subgraph DataAccessLayer["数据访问层 (Data Access Layer)"]
        subgraph MyBatis["MyBatis Plus *******"]
            subgraph MapperInterface["Mapper接口"]
                OrderMapper["OrderMapper"]
                ElderMapper["ElderMapper"]
                BedMapper["BedMapper"]
            end
            
            subgraph EntityPO["实体类(PO)"]
                ElderPO["Elder"]
                BedPO["Bed"]
                OrderPO["Order"]
            end
            
            subgraph QueryObjects["查询对象(Query)"]
                AddOrderQuery["AddOrderQuery"]
                PageOrderQuery["PageOrderByKeyQuery"]
            end
            
            subgraph VOObjects["响应对象(VO)"]
                LoginUserVo["LoginUserVo"]
                GetOrderVo["GetOrderByIdVo"]
            end
            
            XMLMapper["XML映射文件"]
        end
    end

    %% 第五层：数据存储层
    subgraph DataStorageLayer["数据存储层 (Data Storage Layer)"]
        subgraph MySQL["MySQL 5.7"]
            subgraph Database["db_gerocomium"]
                subgraph UserTables["用户相关表"]
                    StaffTable["staff(员工表)"]
                    AuthTable["auth(权限表)"]
                    RoleTable["role(角色表)"]
                end
                
                subgraph BusinessTables["业务相关表"]
                    ElderTable["elder(老人表)"]
                    BedTable["bed(床位表)"]
                    OrderTable["order(订餐表)"]
                end
                
                subgraph ManagementTables["管理相关表"]
                    BuildingTable["building(楼栋表)"]
                    FloorTable["floor(楼层表)"]
                    RoomTable["room(房间表)"]
                end
            end
        end
        
        subgraph Redis["Redis 6.2.13"]
            subgraph CacheData["缓存数据"]
                UserSession["用户会话管理"]
                PermissionCache["权限数据缓存"]
                JwtCache["JWT Token缓存"]
                BusinessCache["业务数据缓存"]
            end
        end
        
        Druid["Druid连接池<br/>数据库连接管理<br/>性能监控"]
    end

    %% 连接关系
    User --> Frontend
    Frontend --> BackendController
    BackendController --> Shiro
    Shiro --> ServiceLayer
    ServiceLayer --> MyBatis
    MyBatis --> MySQL
    MyBatis --> Redis
    Druid --> MySQL

    %% 应用样式
    class Vue,Router,Vuex,Element,HomeView,LoginView,Layout,AccountCtrl,BuildCtrl,OrderCtrl,RestAPI,Swagger presentationStyle
    class ShiroConfig,JwtRealm,JwtFilter,JwtToken,ShiroUtils,PasswordEncoder,AuthorityAssert securityStyle
    class AccountService,BuildService,OrderService,AccountServiceImpl,BuildServiceImpl,OrderServiceImpl,BuildingFunc,ElderFunc,BedFunc businessStyle
    class OrderMapper,ElderMapper,BedMapper,ElderPO,BedPO,OrderPO,AddOrderQuery,PageOrderQuery,LoginUserVo,GetOrderVo,XMLMapper dataAccessStyle
    class StaffTable,AuthTable,RoleTable,ElderTable,BedTable,OrderTable,BuildingTable,FloorTable,RoomTable,UserSession,PermissionCache,JwtCache,BusinessCache,Druid dataStorageStyle
```

## 层次架构说明

### 1. 表示层 (Presentation Layer)
- **前端**: Vue 3 + TypeScript + Element Plus
- **后端**: Spring Boot Controller + RESTful API
- **职责**: 用户界面展示和用户交互处理

### 2. 安全层 (Security Layer)
- **框架**: Apache Shiro 1.9.1
- **组件**: JWT认证、权限控制、密码加密
- **职责**: 系统安全认证和授权管理

### 3. 业务逻辑层 (Business Logic Layer)
- **组件**: Service接口和实现类、Func功能类
- **职责**: 核心业务逻辑处理、数据验证、事务管理

### 4. 数据访问层 (Data Access Layer)
- **框架**: MyBatis Plus *******
- **组件**: Mapper接口、实体类、查询对象、响应对象
- **职责**: 数据持久化操作和数据转换

### 5. 数据存储层 (Data Storage Layer)
- **数据库**: MySQL 5.7 (主数据库)
- **缓存**: Redis 6.2.13 (会话和权限缓存)
- **连接池**: Druid (数据库连接管理)
- **职责**: 数据存储和管理

## 架构特点

1. **分层清晰**: 每层职责明确，高内聚低耦合
2. **安全独立**: 安全层独立于业务逻辑，便于安全策略管理
3. **技术先进**: 采用现代化技术栈，支持前后端分离
4. **扩展性强**: 分层架构便于系统维护和功能扩展
5. **性能优化**: 使用Redis缓存和Druid连接池提升性能
